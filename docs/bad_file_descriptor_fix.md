# Bad File Descriptor 错误修复方案

## 问题描述

在使用 Golang HTTP 客户端访问 `http://*************/xiaozhi/config/agent-models` 时，遇到了 "dial tcp *************:80: connect: bad file descriptor" 错误，但使用 curl 命令可以正常连接。

## 问题原因

"bad file descriptor" 错误通常由以下原因引起：

1. **连接池配置不当** - 连接池过大导致文件描述符耗尽
2. **连接没有正确关闭** - 长期积累的未关闭连接
3. **HTTP客户端配置问题** - 超时设置、Keep-Alive 配置等
4. **系统级别的文件描述符限制** - 操作系统限制

## 解决方案

### 1. HTTP 客户端配置优化

在 `internal/manager_api/client.go` 中优化了 HTTP 传输层配置：

```go
transport := &http.Transport{
    // 连接池配置 - 减少连接数以避免文件描述符耗尽
    MaxIdleConns:        50,   // 从100减少到50
    MaxIdleConnsPerHost: 5,    // 从10减少到5
    MaxConnsPerHost:     10,   // 限制每个主机的最大连接数
    IdleConnTimeout:     30 * time.Second,  // 从90s减少到30s
    
    // 连接超时配置
    Dial: (&net.Dialer{
        Timeout: 10 * time.Second,
    }).Dial,
    TLSHandshakeTimeout: 10 * time.Second,
    ResponseHeaderTimeout: 30 * time.Second,
    ExpectContinueTimeout: 1 * time.Second,
    
    // 禁用HTTP/2，避免兼容性问题
    ForceAttemptHTTP2: false,
}
```

### 2. 连接管理改进

- **添加连接清理方法**：实现 `Close()` 方法主动关闭空闲连接
- **强制关闭连接**：在请求头中添加 `Connection: close`，避免连接重用问题
- **连接诊断日志**：添加详细的连接初始化日志

### 3. 错误处理和重试机制

实现了智能重试机制 `doRequestWithRetry()`：

```go
func (c *HTTPClient) doRequestWithRetry(req *http.Request, operation string) (*http.Response, error) {
    maxRetries := 3
    baseDelay := time.Millisecond * 500
    
    for attempt := 0; attempt <= maxRetries; attempt++ {
        resp, err := c.httpClient.Do(req)
        if err != nil {
            // 检查是否是bad file descriptor错误
            if strings.Contains(err.Error(), "bad file descriptor") && attempt < maxRetries {
                // 强制关闭空闲连接
                if transport, ok := c.httpClient.Transport.(*http.Transport); ok {
                    transport.CloseIdleConnections()
                }
                
                // 指数退避重试
                delay := baseDelay * time.Duration(1<<attempt)
                time.Sleep(delay)
                continue
            }
            return nil, err
        }
        return resp, nil
    }
    
    return nil, fmt.Errorf("max retries exceeded for operation: %s", operation)
}
```

### 4. 详细的错误日志

为每个HTTP请求添加了详细的错误日志记录：

- 记录具体的错误信息和请求URL
- 识别 "bad file descriptor" 和 "connection refused" 等特定错误
- 提供连接池状态信息

## 测试验证

创建了测试工具 `test_manager_api_connection.go` 来验证修复效果：

```bash
go run test_manager_api_connection.go
```

测试包括：
1. HTTP客户端创建
2. 健康检查
3. 多次请求测试（验证连接池稳定性）
4. Agent Models配置获取测试

## 使用指南

### 1. 更新现有代码

修改后的代码已经自动应用到所有HTTP请求方法：
- `GetServerConfig()`
- `GetAgentModels()`
- `SaveMemory()`
- `ReportChatHistory()`
- `HealthCheck()`

### 2. 监控和调试

启用详细日志来监控连接状态：

```yaml
log:
  level: "debug"  # 启用调试日志
```

### 3. 配置优化

如果仍然遇到问题，可以进一步调整配置：

```yaml
manager_api:
  timeout_seconds: 30        # 适当增加超时时间
  base_url: "http://*************/xiaozhi"  # 确保URL正确
```

## 最佳实践

1. **监控文件描述符使用情况**
   ```bash
   # 查看进程打开的文件描述符数量
   lsof -p <pid> | wc -l
   
   # 查看系统限制
   ulimit -n
   ```

2. **定期清理连接**
   - HTTP客户端会自动清理过期连接
   - 应用重启时会自动清理所有连接

3. **错误监控**
   - 监控日志中的 "bad file descriptor" 错误
   - 关注连接超时和拒绝连接错误

## 故障排除

如果问题仍然存在：

1. **检查网络连接**
   ```bash
   curl -v http://*************/xiaozhi/config/agent-models
   ```

2. **检查系统限制**
   ```bash
   ulimit -n  # 查看文件描述符限制
   ```

3. **查看详细日志**
   - 启用 debug 级别日志
   - 查找具体的错误信息

4. **尝试禁用连接重用**
   - 如果问题严重，可以临时设置 `DisableKeepAlives: true`

## 相关文件

- `internal/manager_api/client.go` - HTTP客户端实现
- `config/config.yaml` - 配置文件
- `test_manager_api_connection.go` - 测试工具
- `docs/bad_file_descriptor_fix.md` - 本文档

## 联系信息

如果问题仍然存在，请提供：
- 完整的错误日志
- 系统环境信息（操作系统、Go版本等）
- 网络环境详情
