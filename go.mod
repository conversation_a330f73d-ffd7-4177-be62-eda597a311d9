module xiaozhi-esp32-server-golang

go 1.24.0

toolchain go1.24.4

require (
	github.com/ThinkInAIXYZ/go-mcp v0.2.21
	github.com/aliyun/alibabacloud-nls-go-sdk v1.1.1
	github.com/antonfisher/nested-logrus-formatter v1.3.1
	github.com/bytedance/sonic v1.14.0
	github.com/cloudwego/eino v0.4.6
	github.com/cloudwego/eino-ext/components/model/ollama v0.1.1-0.20250814083140-54b99ff82f8e
	github.com/cloudwego/eino-ext/components/model/openai v0.0.0-20250822083409-f8d432eea60f
	github.com/delucks/go-subsonic v0.0.0-20240806025900-2a743ec36238
	github.com/difyz9/edge-tts-go v0.0.2
	github.com/eclipse/paho.mqtt.golang v1.5.0
	github.com/getkin/kin-openapi v0.118.0
	github.com/go-audio/audio v1.0.0
	github.com/go-audio/wav v1.1.0
	github.com/google/uuid v1.6.0
	github.com/gopxl/beep v1.4.1
	github.com/gorilla/websocket v1.5.3
	github.com/hackers365/go-webrtcvad v0.0.0-20250711024710-dde35479e077
	github.com/hraban/opus v0.0.0-20220302220929-eeacdbcb92d0
	github.com/k2-fsa/sherpa-onnx-go v1.12.9
	github.com/lestrrat-go/file-rotatelogs v2.4.0+incompatible
	github.com/mark3labs/mcp-go v0.36.0
	github.com/memodb-io/memobase/src/client/memobase-go v0.0.0-20250730050435-c4d285d32bcd
	github.com/mochi-mqtt/server/v2 v2.7.9
	github.com/orcaman/concurrent-map/v2 v2.0.1
	github.com/redis/go-redis/v9 v9.7.3
	github.com/sirupsen/logrus v1.9.3
	github.com/spf13/viper v1.20.1
	github.com/streamer45/silero-vad-go v0.2.1
	github.com/stretchr/testify v1.10.0
	go.uber.org/zap v1.27.0
	gopkg.in/hraban/opus.v2 v2.0.0-20230925203106-0188a62cb302
)

require (
	github.com/aliyun/alibaba-cloud-sdk-go v1.61.1376 // indirect
	github.com/bahlo/generic-list-go v0.2.0 // indirect
	github.com/buger/jsonparser v1.1.1 // indirect
	github.com/bytedance/sonic/loader v0.3.0 // indirect
	github.com/cespare/xxhash/v2 v2.3.0 // indirect
	github.com/cloudwego/base64x v0.1.5 // indirect
	github.com/cloudwego/eino-ext/libs/acl/openai v0.0.0-20250821122458-ae35393076b3 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/dustin/go-humanize v1.0.1 // indirect
	github.com/eino-contrib/jsonschema v1.0.0 // indirect
	github.com/evanphx/json-patch v0.5.2 // indirect
	github.com/fsnotify/fsnotify v1.8.0 // indirect
	github.com/go-audio/riff v1.0.0 // indirect
	github.com/go-openapi/jsonpointer v0.19.5 // indirect
	github.com/go-openapi/swag v0.19.5 // indirect
	github.com/go-viper/mapstructure/v2 v2.2.1 // indirect
	github.com/goph/emperror v0.17.2 // indirect
	github.com/hajimehoshi/go-mp3 v0.3.4 // indirect
	github.com/invopop/jsonschema v0.13.0 // indirect
	github.com/invopop/yaml v0.1.0 // indirect
	github.com/jmespath/go-jmespath v0.4.0 // indirect
	github.com/jonboulle/clockwork v0.5.0 // indirect
	github.com/josharian/intern v1.0.0 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/k2-fsa/sherpa-onnx-go-linux v1.12.9 // indirect
	github.com/k2-fsa/sherpa-onnx-go-macos v1.12.9 // indirect
	github.com/k2-fsa/sherpa-onnx-go-windows v1.12.9 // indirect
	github.com/klauspost/cpuid/v2 v2.2.7 // indirect
	github.com/lestrrat-go/strftime v1.1.0 // indirect
	github.com/mailru/easyjson v0.7.7 // indirect
	github.com/meguminnnnnnnnn/go-openai v0.0.0-20250821095446-07791bea23a0 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/mohae/deepcopy v0.0.0-20170929034955-c48cc78d4826 // indirect
	github.com/nikolalohinski/gonja v1.5.3 // indirect
	github.com/ollama/ollama v0.9.6 // indirect
	github.com/pelletier/go-toml/v2 v2.2.3 // indirect
	github.com/perimeterx/marshmallow v1.1.4 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/rs/xid v1.4.0 // indirect
	github.com/sagikazarmark/locafero v0.7.0 // indirect
	github.com/satori/go.uuid v1.2.0 // indirect
	github.com/slongfield/pyfmt v0.0.0-20220222012616-ea85ff4c361f // indirect
	github.com/sourcegraph/conc v0.3.0 // indirect
	github.com/spf13/afero v1.12.0 // indirect
	github.com/spf13/cast v1.7.1 // indirect
	github.com/spf13/pflag v1.0.6 // indirect
	github.com/stretchr/objx v0.5.2 // indirect
	github.com/subosito/gotenv v1.6.0 // indirect
	github.com/tidwall/gjson v1.18.0 // indirect
	github.com/tidwall/match v1.1.1 // indirect
	github.com/tidwall/pretty v1.2.0 // indirect
	github.com/twitchyliquid64/golang-asm v0.15.1 // indirect
	github.com/wk8/go-ordered-map/v2 v2.1.8 // indirect
	github.com/yargevad/filepathx v1.0.0 // indirect
	github.com/yosida95/uritemplate/v3 v3.0.2 // indirect
	go.uber.org/multierr v1.10.0 // indirect
	golang.org/x/arch v0.11.0 // indirect
	golang.org/x/crypto v0.39.0 // indirect
	golang.org/x/exp v0.0.0-20250218142911-aa4b98e5adaa // indirect
	golang.org/x/net v0.38.0 // indirect
	golang.org/x/sync v0.16.0 // indirect
	golang.org/x/sys v0.35.0 // indirect
	golang.org/x/text v0.28.0 // indirect
	gopkg.in/ini.v1 v1.67.0 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)
