package manager_api

import (
	"context"
	"strings"
	"sync"
	"time"

	"xiaozhi-esp32-server-golang/internal/manager_api/types"
)

// ManagerAPIClient defines the main client interface for manager-api integration
type ManagerAPIClient interface {
	// GetServerConfig retrieves server configuration from manager-api
	GetServerConfig(ctx context.Context) (*types.ServerConfig, error)

	// GetAgentModels retrieves agent model configuration for a specific device
	GetAgentModels(ctx context.Context, req *types.AgentModelsRequest) (*types.AgentModelsConfig, error)

	// SaveMemory saves device summary memory to manager-api
	SaveMemory(ctx context.Context, macAddress string, req *types.MemoryRequest) error

	// ReportChatHistory reports chat history and audio data to manager-api
	ReportChatHistory(ctx context.Context, req *types.ChatHistoryRequest) error

	// HealthCheck performs a health check against manager-api
	HealthCheck(ctx context.Context) error
}

// ConfigProvider handles configuration retrieval and caching
type ConfigProvider interface {
	// GetServerConfig retrieves and caches server configuration
	GetServerConfig(ctx context.Context) (*types.ServerConfig, error)

	// GetDeviceConfig retrieves device-specific configuration
	GetDeviceConfig(ctx context.Context, macAddress, clientID string, modules map[string]string) (*types.AgentModelsConfig, error)

	// RefreshConfig forces a refresh of cached configurations
	RefreshConfig(ctx context.Context) error

	// InvalidateCache clears all cached configurations
	InvalidateCache()
}

// MemoryManager handles memory operations
type MemoryManager interface {
	// SaveDeviceMemory saves conversation summary memory for a device
	SaveDeviceMemory(ctx context.Context, macAddress, summaryMemory string) error

	// GetDeviceMemory retrieves stored memory for a device (if supported)
	GetDeviceMemory(ctx context.Context, macAddress string) (string, error)
}

// ChatHistoryReporter handles chat history reporting
type ChatHistoryReporter interface {
	// ReportChat reports a single chat message with optional audio data
	ReportChat(ctx context.Context, macAddress, sessionID, chatType, content string, audioData []byte) error

	// ReportBatchChat reports multiple chat messages in batch
	ReportBatchChat(ctx context.Context, requests []*types.ChatHistoryRequest) error
}

// ManagerAPIService combines all manager-api operations
type ManagerAPIService interface {
	ConfigProvider
	MemoryManager
	ChatHistoryReporter

	// Start initializes the service and performs initial setup
	Start(ctx context.Context) error

	// Stop gracefully shuts down the service
	Stop(ctx context.Context) error

	// IsHealthy returns current health status
	IsHealthy() bool
}

// Config represents the manager-api configuration
type Config struct {
	BaseURL              string        `json:"base_url"` // 支持逗号分隔的多个地址
	Secret               string        `json:"secret"`
	TimeoutSeconds       int           `json:"timeout_seconds"`
	Enabled              bool          `json:"enabled"`
	BatchSize            int           `json:"batch_size"`
	FlushIntervalSeconds int           `json:"flush_interval_seconds"`
	HealthCheckInterval  time.Duration `json:"-"`
	MaxRetries           int           `json:"max_retries"` // 最大重试次数
	RetryDelay           time.Duration `json:"-"`           // 重试延迟
	// 内部字段
	ParsedURLs []string `json:"-"` // 解析后的URL列表
}

// DefaultConfig returns default configuration values
func DefaultConfig() *Config {
	return &Config{
		BaseURL:              "http://localhost:8080",
		Secret:               "",
		TimeoutSeconds:       30,
		Enabled:              true,
		BatchSize:            50,
		FlushIntervalSeconds: 10,
		HealthCheckInterval:  30 * time.Second,
		MaxRetries:           3,
		RetryDelay:           500 * time.Millisecond,
	}
}

// Validate validates the configuration
func (c *Config) Validate() error {
	if c.BaseURL == "" {
		return NewConfigError("base_url", "base_url is required")
	}
	if c.Secret == "" {
		return NewConfigError("secret", "secret is required")
	}
	if c.TimeoutSeconds <= 0 {
		return NewConfigError("timeout_seconds", "timeout_seconds must be positive")
	}
	if c.BatchSize <= 0 {
		c.BatchSize = 50 // Default value
	}
	if c.FlushIntervalSeconds <= 0 {
		c.FlushIntervalSeconds = 10 // Default value
	}
	if c.MaxRetries <= 0 {
		c.MaxRetries = 3 // Default value
	}
	if c.RetryDelay <= 0 {
		c.RetryDelay = 500 * time.Millisecond // Default value
	}

	// Parse multiple URLs
	c.ParseURLs()
	return nil
}

// ParseURLs parses and validates multiple URLs
func (c *Config) ParseURLs() {
	// Clear previous results
	c.ParsedURLs = nil

	// Split BaseURL by comma
	urls := strings.Split(c.BaseURL, ",")
	for _, url := range urls {
		url = strings.TrimSpace(url)
		if url != "" {
			c.ParsedURLs = append(c.ParsedURLs, url)
		}
	}

	// If no valid URLs, use default
	if len(c.ParsedURLs) == 0 {
		c.ParsedURLs = []string{"http://localhost:8080"}
	}
}

// URLManager 管理多个URL的轮询和重试机制
type URLManager struct {
	urls        []string
	currentIdx  int
	lastSuccess string
	mutex       sync.RWMutex
}

// NewURLManager 创建新的URL管理器
func NewURLManager(urls []string) *URLManager {
	if len(urls) == 0 {
		urls = []string{"http://localhost:8080"}
	}
	um := &URLManager{
		urls:       make([]string, len(urls)),
		currentIdx: 0,
	}
	copy(um.urls, urls)
	return um
}

// GetCurrentURL 获取当前使用的URL
func (um *URLManager) GetCurrentURL() string {
	um.mutex.RLock()
	defer um.mutex.RUnlock()

	if len(um.urls) == 0 {
		return "http://localhost:8080"
	}
	return um.urls[um.currentIdx]
}

// GetAllURLs 获取所有URL用于重试
func (um *URLManager) GetAllURLs() []string {
	um.mutex.RLock()
	defer um.mutex.RUnlock()

	result := make([]string, len(um.urls))
	copy(result, um.urls)
	return result
}

// MarkSuccess 标记URL为成功，并将其移到第一位
func (um *URLManager) MarkSuccess(url string) {
	um.mutex.Lock()
	defer um.mutex.Unlock()

	um.lastSuccess = url

	// 找到成功的URL并移动到第一位
	for i, u := range um.urls {
		if u == url && i != 0 {
			// 移动成功的URL到第一位
			um.urls[0], um.urls[i] = um.urls[i], um.urls[0]
			um.currentIdx = 0
			break
		}
	}
}

// MarkFailure 标记URL失败，轮换到下一个URL
func (um *URLManager) MarkFailure(url string) {
	um.mutex.Lock()
	defer um.mutex.Unlock()

	// 切换到下一个URL
	um.currentIdx = (um.currentIdx + 1) % len(um.urls)
}

// GetNextURL 获取下一个URL（轮询）
func (um *URLManager) GetNextURL() string {
	um.mutex.Lock()
	defer um.mutex.Unlock()

	if len(um.urls) <= 1 {
		return um.GetCurrentURL()
	}

	um.currentIdx = (um.currentIdx + 1) % len(um.urls)
	return um.urls[um.currentIdx]
}

// Reset 重置URL管理器，将上次成功的URL放到第一位
func (um *URLManager) Reset(urls []string) {
	um.mutex.Lock()
	defer um.mutex.Unlock()

	if len(urls) == 0 {
		return
	}

	um.urls = make([]string, len(urls))
	copy(um.urls, urls)

	// 如果有上次成功的URL，将其放到第一位
	if um.lastSuccess != "" {
		for i, url := range um.urls {
			if url == um.lastSuccess && i != 0 {
				um.urls[0], um.urls[i] = um.urls[i], um.urls[0]
				break
			}
		}
	}

	um.currentIdx = 0
}
