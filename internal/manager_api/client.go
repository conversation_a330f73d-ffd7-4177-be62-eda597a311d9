package manager_api

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/url"
	"strings"
	"time"

	"xiaozhi-esp32-server-golang/internal/manager_api/types"
	"xiaozhi-esp32-server-golang/logger"
)

// HTTPClient implements the ManagerAPIClient interface
type HTTPClient struct {
	config     *Config
	httpClient *http.Client
	urlManager *URLManager
}

// Close closes the HTTP client and cleans up connections
func (c *HTTPClient) Close() error {
	if transport, ok := c.httpClient.Transport.(*http.Transport); ok {
		transport.CloseIdleConnections()
		logger.Info("HTTP client connections closed")
	}
	return nil
}

// NewHTTPClient creates a new HTTP client for manager-api
func NewHTTPClient(config *Config) (*HTTPClient, error) {
	if err := config.Validate(); err != nil {
		return nil, err
	}

	// 创建一个更稳定的HTTP传输层配置
	transport := &http.Transport{
		// 连接池配置 - 减少连接数以避免文件描述符耗尽
		MaxIdleConns:        50,               // 减少到50
		MaxIdleConnsPerHost: 5,                // 减少到5
		MaxConnsPerHost:     10,               // 限制每个主机的最大连接数
		IdleConnTimeout:     30 * time.Second, // 减少空闲连接时间

		// 连接超时配置 - 使用Dial函数来设置超时
		Dial: (&net.Dialer{
			Timeout: 10 * time.Second,
		}).Dial,
		TLSHandshakeTimeout:   10 * time.Second,
		ResponseHeaderTimeout: 30 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,

		// 强制关闭连接，避免连接重用问题
		DisableKeepAlives: false, // 启用Keep-Alive，但设置合理的超时

		// 其他优化设置
		ForceAttemptHTTP2: false, // 禁用HTTP/2，避免兼容性问题
	}

	httpClient := &http.Client{
		Timeout:   time.Duration(config.TimeoutSeconds) * time.Second,
		Transport: transport,
	}

	// Validate URL format
	if _, err := url.Parse(config.ParsedURLs[0]); err != nil {
		return nil, NewConfigError("base_url", fmt.Sprintf("invalid URL format: %s", config.ParsedURLs[0]))
	}

	client := &HTTPClient{
		config:     config,
		httpClient: httpClient,
		urlManager: NewURLManager(config.ParsedURLs),
	}

	// 添加连接诊断
	logger.Infof("HTTP Client initialized with config: base_url=%s, timeout=%ds",
		config.ParsedURLs[0], config.TimeoutSeconds)
	logger.Debugf("Transport settings: MaxIdleConns=%d, MaxIdleConnsPerHost=%d, MaxConnsPerHost=%d",
		50, 5, 10)

	return client, nil
}

// executeRequestWithRetry 执行HTTP请求，带有多URL重试机制
func (c *HTTPClient) executeRequestWithRetry(ctx context.Context, method, path string, body io.Reader, operation string) (*http.Response, error) {
	urls := c.urlManager.GetAllURLs()
	maxRetries := c.config.MaxRetries
	if maxRetries <= 0 {
		maxRetries = 3
	}

	var lastErr error

	// 遍历所有URL进行重试
	for _, baseURL := range urls {
		for attempt := 0; attempt < maxRetries; attempt++ {
			// 创建请求
			fullURL := strings.TrimSuffix(baseURL, "/") + path
			req, err := http.NewRequestWithContext(ctx, method, fullURL, body)
			if err != nil {
				lastErr = NewError(ErrorCodeInternalError, "failed to create request")
				continue
			}

			// 设置请求头
			req.Header.Set("Authorization", "Bearer "+c.config.Secret)
			req.Header.Set("User-Agent", "xiaozhi-backend-server/1.0")
			if method == "POST" || method == "PUT" {
				req.Header.Set("Content-Type", "application/json")
			}

			// 执行请求
			resp, err := c.httpClient.Do(req)
			if err != nil {
				logger.Warnf("Request failed for %s (attempt %d/%d): %v", baseURL, attempt+1, maxRetries, err)
				lastErr = NewNetworkError(operation, err)

				// 如果是连接问题，进行指数退避重试
				if attempt < maxRetries-1 {
					delay := c.config.RetryDelay * time.Duration(1<<attempt)
					time.Sleep(delay)
				}
				continue
			}

			// 请求成功，标记URL为成功
			if resp.StatusCode < http.StatusInternalServerError {
				c.urlManager.MarkSuccess(baseURL)
				return resp, nil
			}

			// 服务器错误，尝试重试
			resp.Body.Close()
			lastErr = NewAPIError(resp.StatusCode, fmt.Sprintf("HTTP %d", resp.StatusCode), "")

			if attempt < maxRetries-1 {
				delay := c.config.RetryDelay * time.Duration(1<<attempt)
				time.Sleep(delay)
			}
		}

		// 标记URL失败
		c.urlManager.MarkFailure(baseURL)
	}

	return nil, lastErr
}

// GetServerConfig retrieves server configuration from manager-api
func (c *HTTPClient) GetServerConfig(ctx context.Context) (*types.ServerConfig, error) {
	return c.getServerConfigRequest(ctx)
}

// getServerConfigRequest performs the actual HTTP request for server config
func (c *HTTPClient) getServerConfigRequest(ctx context.Context) (*types.ServerConfig, error) {
	resp, err := c.executeRequestWithRetry(ctx, "POST", "/config/server-base", nil, "get server config")
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, c.handleHTTPError(resp)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, NewNetworkError("read response body", err)
	}

	// 使用struct直接解析响应
	var config types.ServerConfig
	if err := json.Unmarshal(body, &config); err != nil {
		return nil, NewAPIError(resp.StatusCode, "invalid JSON response", "")
	}

	// 保存原始响应数据用于Additional字段
	var additional map[string]interface{}
	json.Unmarshal(body, &additional)
	config.Additional = additional

	return &config, nil
}

// GetAgentModels retrieves agent model configuration for a specific device
func (c *HTTPClient) GetAgentModels(ctx context.Context, req *types.AgentModelsRequest) (*types.AgentModelsConfig, error) {
	logger.Debug("GetAgentModels request")
	return c.getAgentModelsRequest(ctx, req)
}

// getAgentModelsRequest performs the actual HTTP request for agent models config
func (c *HTTPClient) getAgentModelsRequest(ctx context.Context, req *types.AgentModelsRequest) (*types.AgentModelsConfig, error) {
	body, err := json.Marshal(req)
	if err != nil {
		return nil, NewError(ErrorCodeInternalError, "failed to marshal request")
	}

	resp, err := c.executeRequestWithRetry(ctx, "POST", "/config/agent-models", bytes.NewReader(body), "get agent models")
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, c.handleHTTPError(resp)
	}

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, NewNetworkError("read response body", err)
	}

	logger.Debugf("getAgentModelsRequest respBody: %s", string(respBody))

	// 使用struct解析包装的响应格式
	type AgentModelsResponse struct {
		Code int                      `json:"code"`
		Msg  string                   `json:"msg"`
		Data *types.AgentModelsConfig `json:"data"`
	}

	var response AgentModelsResponse
	if err := json.Unmarshal(respBody, &response); err != nil {
		return nil, NewAPIError(resp.StatusCode, "invalid JSON response", "")
	}

	if response.Code != 0 {
		return nil, NewAPIError(resp.StatusCode, response.Msg, fmt.Sprintf("%d", response.Code))
	}

	return response.Data, nil
}

// SaveMemory saves device summary memory to manager-api
func (c *HTTPClient) SaveMemory(ctx context.Context, macAddress string, req *types.MemoryRequest) error {
	return c.saveMemoryRequest(ctx, macAddress, req)
}

// saveMemoryRequest performs the actual HTTP request for saving memory
func (c *HTTPClient) saveMemoryRequest(ctx context.Context, macAddress string, req *types.MemoryRequest) error {
	body, err := json.Marshal(req)
	if err != nil {
		return NewError(ErrorCodeInternalError, "failed to marshal memory request")
	}

	endpoint := fmt.Sprintf("/agent/saveMemory/%s", macAddress)
	resp, err := c.executeRequestWithRetry(ctx, "PUT", endpoint, bytes.NewReader(body), "save memory")
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	respBody, _ := io.ReadAll(resp.Body)
	logger.Infof("saveMemory response body: %s", string(respBody))

	if resp.StatusCode != http.StatusOK {
		return c.handleHTTPError(resp)
	}

	return nil
}

// ReportChatHistory reports chat history and audio data to manager-api
func (c *HTTPClient) ReportChatHistory(ctx context.Context, req *types.ChatHistoryRequest) error {
	return c.reportChatHistoryRequest(ctx, req)
}

// reportChatHistoryRequest performs the actual HTTP request for reporting chat history
func (c *HTTPClient) reportChatHistoryRequest(ctx context.Context, req *types.ChatHistoryRequest) error {
	body, err := json.Marshal(req)
	if err != nil {
		return NewError(ErrorCodeInternalError, "failed to marshal chat history request")
	}

	resp, err := c.executeRequestWithRetry(ctx, "POST", "/agent/chat-history/report", bytes.NewReader(body), "report chat history")
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return c.handleHTTPError(resp)
	}

	return nil
}

// HealthCheck performs a health check against manager-api
func (c *HTTPClient) HealthCheck(ctx context.Context) error {
	resp, err := c.executeRequestWithRetry(ctx, "GET", "/health", nil, "health check")
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	logger.Debugf("HealthCheck response status: %d", resp.StatusCode)
	if resp.StatusCode >= http.StatusOK && resp.StatusCode < http.StatusInternalServerError {
		return nil
	}

	return NewAPIError(resp.StatusCode, "health check failed", "")
}

// handleHTTPError handles HTTP error responses
func (c *HTTPClient) handleHTTPError(resp *http.Response) error {
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return NewAPIError(resp.StatusCode, fmt.Sprintf("HTTP %d: failed to read error response", resp.StatusCode), "")
	}

	// 尝试解析标准错误响应格式
	type ErrorResponse struct {
		Message string `json:"message"`
		Code    string `json:"code"`
		Msg     string `json:"msg"` // 兼容不同的响应格式
	}
	
	var errorResp ErrorResponse
	if json.Unmarshal(body, &errorResp) == nil && (errorResp.Message != "" || errorResp.Msg != "") {
		// 统一错误消息处理
		message := errorResp.Message
		if message == "" {
			message = errorResp.Msg
		}
		
		// 处理特定的业务逻辑错误
		switch errorResp.Code {
		case "10041":
			return NewError(ErrorCodeDeviceNotFound, "Device not found: "+message).WithStatusCode(resp.StatusCode).WithDetail("business_code", errorResp.Code)
		case "10042":
			return NewError(ErrorCodeDeviceBindError, "Device bind error: "+message).WithStatusCode(resp.StatusCode).WithDetail("business_code", errorResp.Code)
		default:
			return NewAPIError(resp.StatusCode, message, errorResp.Code)
		}
	}

	// 处理认证错误
	if resp.StatusCode == http.StatusUnauthorized {
		return NewAuthError("Invalid or missing authentication token")
	}

	return NewAPIError(resp.StatusCode, fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body)), "")
}

// IsHealthy returns current health status
func (c *HTTPClient) IsHealthy() bool {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	return c.HealthCheck(ctx) == nil
}
