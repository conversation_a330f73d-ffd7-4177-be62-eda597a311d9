package manager_api

import (
	"fmt"
	"time"
)

// ErrorCode 定义错误码类型
type ErrorCode string

const (
	ErrorCodeInvalidConfig   ErrorCode = "INVALID_CONFIG"
	ErrorCodeNetworkError    ErrorCode = "NETWORK_ERROR"
	ErrorCodeAPIError        ErrorCode = "API_ERROR"
	ErrorCodeAuthError       ErrorCode = "AUTH_ERROR"
	ErrorCodeTimeout         ErrorCode = "TIMEOUT"
	ErrorCodeServiceUnavail  ErrorCode = "SERVICE_UNAVAILABLE"
	ErrorCodeDeviceNotFound  ErrorCode = "DEVICE_NOT_FOUND"
	ErrorCodeDeviceBindError ErrorCode = "DEVICE_BIND_ERROR"
	ErrorCodeInternalError   ErrorCode = "INTERNAL_ERROR"
)

// ManagerError 统一的错误结构
type ManagerError struct {
	Code       ErrorCode         `json:"code"`
	Message    string            `json:"message"`
	Details    map[string]string `json:"details,omitempty"`
	StatusCode int               `json:"status_code,omitempty"`
	Timestamp  time.Time         `json:"timestamp"`
	Operation  string            `json:"operation,omitempty"`
}

// Error 实现error接口
func (e ManagerError) Error() string {
	if e.Operation != "" {
		return fmt.Sprintf("[%s] %s: %s", e.Code, e.Operation, e.Message)
	}
	return fmt.Sprintf("[%s] %s", e.Code, e.Message)
}

// NewError 创建新的错误
func NewError(code ErrorCode, message string, operation ...string) ManagerError {
	err := ManagerError{
		Code:      code,
		Message:   message,
		Timestamp: time.Now(),
		Details:   make(map[string]string),
	}
	if len(operation) > 0 {
		err.Operation = operation[0]
	}
	return err
}

// NewConfigError 创建配置错误
func NewConfigError(field, message string) ManagerError {
	err := NewError(ErrorCodeInvalidConfig, message)
	err.Details["field"] = field
	return err
}

// NewNetworkError 创建网络错误
func NewNetworkError(operation string, underlying error) ManagerError {
	err := NewError(ErrorCodeNetworkError, underlying.Error(), operation)
	err.Details["underlying"] = underlying.Error()
	return err
}

// NewAPIError 创建API错误
func NewAPIError(statusCode int, message, businessCode string) ManagerError {
	err := NewError(ErrorCodeAPIError, message)
	err.StatusCode = statusCode
	if businessCode != "" {
		err.Details["business_code"] = businessCode
	}
	return err
}

// NewAuthError 创建认证错误
func NewAuthError(message string) ManagerError {
	return NewError(ErrorCodeAuthError, message)
}

// NewTimeoutError 创建超时错误
func NewTimeoutError(operation string, duration time.Duration) ManagerError {
	err := NewError(ErrorCodeTimeout, fmt.Sprintf("operation timed out after %s", duration.String()), operation)
	err.Details["duration"] = duration.String()
	return err
}

// NewServiceUnavailableError 创建服务不可用错误
func NewServiceUnavailableError(service, reason string) ManagerError {
	err := NewError(ErrorCodeServiceUnavail, fmt.Sprintf("service %s unavailable: %s", service, reason))
	err.Details["service"] = service
	err.Details["reason"] = reason
	return err
}

// IsRetryable 判断错误是否可重试
func (e ManagerError) IsRetryable() bool {
	switch e.Code {
	case ErrorCodeNetworkError, ErrorCodeTimeout, ErrorCodeServiceUnavail:
		return true
	case ErrorCodeAPIError:
		// 只有特定的HTTP状态码才可重试
		return e.StatusCode == 408 || e.StatusCode == 429 ||
			(e.StatusCode >= 500 && e.StatusCode <= 504)
	default:
		return false
	}
}

// IsAuthenticationError 判断是否为认证错误
func (e ManagerError) IsAuthenticationError() bool {
	return e.Code == ErrorCodeAuthError
}

// WithStatusCode 设置HTTP状态码
func (e ManagerError) WithStatusCode(statusCode int) ManagerError {
	e.StatusCode = statusCode
	return e
}

// WithDetail 添加详细信息
func (e ManagerError) WithDetail(key, value string) ManagerError {
	if e.Details == nil {
		e.Details = make(map[string]string)
	}
	e.Details[key] = value
	return e
}

// GetBusinessCode 获取业务错误码
func (e ManagerError) GetBusinessCode() string {
	if e.Details != nil {
		return e.Details["business_code"]
	}
	return ""
}

// 兼容性类型定义，保持向后兼容
type (
	// ErrInvalidConfig 配置错误类型
	ErrInvalidConfig struct {
		Field   string
		Message string
	}

	// ErrAPIRequest API请求错误类型
	ErrAPIRequest struct {
		StatusCode int
		Message    string
		Code       string
	}

	// ErrNetwork 网络错误类型
	ErrNetwork struct {
		Operation string
		Err       error
	}

	// ErrTimeout 超时错误类型
	ErrTimeout struct {
		Operation string
		Duration  time.Duration
	}

	// ErrAuthentication 认证错误类型
	ErrAuthentication struct {
		Message string
	}

	// ErrServiceUnavailable 服务不可用错误类型
	ErrServiceUnavailable struct {
		Service string
		Reason  string
	}
)

// 实现Error接口（兼容性）
func (e ErrInvalidConfig) Error() string {
	return NewConfigError(e.Field, e.Message).Error()
}

func (e ErrAPIRequest) Error() string {
	return NewAPIError(e.StatusCode, e.Message, e.Code).Error()
}

func (e ErrNetwork) Error() string {
	return NewNetworkError(e.Operation, e.Err).Error()
}

func (e ErrTimeout) Error() string {
	return NewTimeoutError(e.Operation, e.Duration).Error()
}

func (e ErrAuthentication) Error() string {
	return NewAuthError(e.Message).Error()
}

func (e ErrServiceUnavailable) Error() string {
	return NewServiceUnavailableError(e.Service, e.Reason).Error()
}

// IsRetryable 全局重试判断函数
func IsRetryable(err error) bool {
	if managerErr, ok := err.(ManagerError); ok {
		return managerErr.IsRetryable()
	}
	return false
}

// IsAuthError 全局认证错误判断函数
func IsAuthError(err error) bool {
	if managerErr, ok := err.(ManagerError); ok {
		return managerErr.IsAuthenticationError()
	}
	return false
}
