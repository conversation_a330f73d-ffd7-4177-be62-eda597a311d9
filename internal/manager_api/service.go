package manager_api

import (
	"context"
	"fmt"
	"sync"
	"time"

	"xiaozhi-esp32-server-golang/internal/manager_api/types"
	"xiaozhi-esp32-server-golang/logger"
)

// Service implements the ManagerAPIService interface
type Service struct {
	client            ManagerAPIClient
	config            *Config
	chatHistoryBuffer *types.ChatHistoryBuffer
	isRunning         bool
	mutex             sync.RWMutex
}

// NewService creates a new manager-api service
func NewService(config *Config) (*Service, error) {
	if err := config.Validate(); err != nil {
		return nil, err
	}

	client, err := NewHTTPClient(config)
	if err != nil {
		return nil, err
	}

	return &Service{
		client: client,
		config: config,
		chatHistoryBuffer: types.NewChatHistoryBuffer(
			config.BatchSize,
			time.Duration(config.FlushIntervalSeconds)*time.Second,
		),
	}, nil
}

// Start initializes the service
func (s *Service) Start(ctx context.Context) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.isRunning {
		return fmt.Errorf("服务已在运行中")
	}

	if !s.config.Enabled {
		logger.Info("Manager-API 服务已禁用")
		return nil
	}

	logger.Info("正在启动 Manager-API 服务...")

	// 执行初始健康检查
	if err := s.client.HealthCheck(ctx); err != nil {
		logger.Warnf("初始健康检查失败: %v", err)
	} else {
		logger.Info("初始健康检查通过")
	}

	s.isRunning = true
	logger.Info("Manager-API 服务启动成功")
	return nil
}

// Stop gracefully shuts down the service
func (s *Service) Stop(ctx context.Context) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.isRunning {
		return nil
	}

	logger.Info("正在停止 Manager-API 服务...")

	// 刷新剩余的聊天历史记录
	if err := s.flushChatHistory(ctx); err != nil {
		logger.Errorf("关闭时刷新聊天历史记录失败: %v", err)
	}

	s.isRunning = false
	logger.Info("Manager-API 服务已停止")
	return nil
}

// GetServerConfig retrieves server configuration
func (s *Service) GetServerConfig(ctx context.Context) (*types.ServerConfig, error) {
	if !s.config.Enabled {
		return nil, NewServiceUnavailableError("manager-api", "服务未启用")
	}

	config, err := s.client.GetServerConfig(ctx)
	if err != nil {
		return nil, err
	}

	logger.Debug("已获取服务器配置")
	return config, nil
}

// GetDeviceConfig retrieves device-specific configuration
func (s *Service) GetDeviceConfig(ctx context.Context, macAddress, clientID string, modules map[string]string) (*types.AgentModelsConfig, error) {
	if !s.config.Enabled {
		return nil, NewServiceUnavailableError("manager-api", "服务未启用")
	}

	req := &types.AgentModelsRequest{
		MacAddress:     macAddress,
		ClientID:       clientID,
		SelectedModule: modules,
	}

	config, err := s.client.GetAgentModels(ctx, req)
	if err != nil {
		return nil, err
	}
	if config == nil {
		return nil, fmt.Errorf("配置为空")
	}

	logger.Debugf("已获取设备配置: %s", macAddress)
	return config, nil
}

// RefreshConfig 刷新配置（无缓存时为空操作）
func (s *Service) RefreshConfig(ctx context.Context) error {
	if !s.config.Enabled {
		return NewServiceUnavailableError("manager-api", "服务未启用")
	}
	logger.Info("配置刷新请求（无操作）")
	return nil
}

// InvalidateCache 缓存失效（无缓存时为空操作）
func (s *Service) InvalidateCache() {
	logger.Info("缓存失效请求（无操作）")
}

// SaveDeviceMemory saves conversation summary memory for a device
func (s *Service) SaveDeviceMemory(ctx context.Context, macAddress, summaryMemory string) error {
	if !s.config.Enabled {
		return NewServiceUnavailableError("manager-api", "服务未启用")
	}

	req := &types.MemoryRequest{
		SummaryMemory: summaryMemory,
	}

	err := s.client.SaveMemory(ctx, macAddress, req)
	if err != nil {
		logger.Errorf("保存设备 %s 内存失败: %v", macAddress, err)
		return err
	}

	logger.Debugf("设备 %s 内存已保存", macAddress)
	return nil
}

// GetDeviceMemory retrieves stored memory for a device (不支持无缓存检索)
func (s *Service) GetDeviceMemory(ctx context.Context, macAddress string) (string, error) {
	if !s.config.Enabled {
		return "", NewServiceUnavailableError("manager-api", "服务未启用")
	}

	return "", fmt.Errorf("设备 %s 不支持无缓存的内存检索", macAddress)
}

// ReportChat reports a single chat message with optional audio data
func (s *Service) ReportChat(ctx context.Context, macAddress, sessionID, chatType, content string, audioData []byte) error {
	if !s.config.Enabled {
		return NewServiceUnavailableError("manager-api", "服务未启用")
	}

	req := &types.ChatHistoryRequest{
		MacAddress: macAddress,
		SessionID:  sessionID,
		ChatType:   chatType,
		Content:    content,
		ReportTime: time.Now(),
	}

	// 编码音频数据（如果提供）
	if len(audioData) > 0 {
		audioDataObj := &types.AudioData{
			Data:      audioData,
			Size:      len(audioData),
			Timestamp: time.Now(),
		}
		req.AudioBase64 = audioDataObj.EncodeToBase64()
	}

	// 添加到缓冲区进行批处理
	shouldFlush := s.chatHistoryBuffer.Add(*req)

	// 缓冲区满时立即刷新
	if shouldFlush {
		return s.flushChatHistory(ctx)
	}

	return nil
}

// ReportBatchChat reports multiple chat messages in batch
func (s *Service) ReportBatchChat(ctx context.Context, requests []*types.ChatHistoryRequest) error {
	if !s.config.Enabled {
		return NewServiceUnavailableError("manager-api", "服务未启用")
	}

	if len(requests) == 0 {
		return nil
	}

	var lastErr error
	successCount := 0

	for _, req := range requests {
		if err := s.client.ReportChatHistory(ctx, req); err != nil {
			logger.Errorf("设备 %s 聊天历史报告失败: %v", req.MacAddress, err)
			lastErr = err
		} else {
			successCount++
		}
	}

	logger.Infof("批量聊天报告完成: %d/%d 成功", successCount, len(requests))

	if successCount == 0 && lastErr != nil {
		return lastErr
	}

	return nil
}

// IsHealthy 返回当前健康状态
func (s *Service) IsHealthy() bool {
	if !s.config.Enabled {
		return true // 将禁用的服务视为健康状态
	}
	if clientHealthy, ok := s.client.(interface{ IsHealthy() bool }); ok {
		return clientHealthy.IsHealthy()
	}
	return true
}

// flushChatHistory 刷新聊天历史缓冲区
func (s *Service) flushChatHistory(ctx context.Context) error {
	messages := s.chatHistoryBuffer.GetMessages()
	if len(messages) == 0 {
		return nil
	}

	logger.Debugf("正在刷新 %d 条聊天历史消息", len(messages))

	// 转换为请求指针
	requests := make([]*types.ChatHistoryRequest, len(messages))
	for i := range messages {
		requests[i] = &messages[i]
	}

	return s.ReportBatchChat(ctx, requests)
}
