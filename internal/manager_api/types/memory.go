package types

import (
	"time"
)

// MemoryRequest represents the request for saving device memory
type MemoryRequest struct {
	SummaryMemory string `json:"summaryMemory"`
}

// MemoryResponse represents the response from memory save operation
type MemoryResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Code    string `json:"code,omitempty"`
}

// DeviceMemory represents stored memory for a device
type DeviceMemory struct {
	MacAddress    string    `json:"mac_address"`
	SummaryMemory string    `json:"summary_memory"`
	LastUpdated   time.Time `json:"last_updated"`
	Version       int       `json:"version"`
	Size          int       `json:"size"` // Memory size in bytes
}

// MemoryStats represents memory statistics for monitoring
type MemoryStats struct {
	TotalDevices      int       `json:"total_devices"`
	TotalMemorySize   int64     `json:"total_memory_size"`   // Total memory size in bytes
	AverageMemorySize float64   `json:"average_memory_size"` // Average memory size per device
	LastSaveTime      time.Time `json:"last_save_time"`
	SaveOperations    int64     `json:"save_operations"`   // Total number of save operations
	SaveErrors        int64     `json:"save_errors"`       // Total number of save errors
	SaveSuccessRate   float64   `json:"save_success_rate"` // Success rate percentage
}

// MemoryOperationResult represents the result of a memory operation
type MemoryOperationResult struct {
	Success       bool          `json:"success"`
	Error         error         `json:"error,omitempty"`
	Duration      time.Duration `json:"duration"`
	MacAddress    string        `json:"mac_address"`
	OperationType string        `json:"operation_type"` // "save", "get", "delete"
	MemorySize    int           `json:"memory_size"`
	Timestamp     time.Time     `json:"timestamp"`
}


